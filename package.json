{"name": "sportheca-athletes-data-collector", "version": "1.0.0", "description": "Sistema automatizado de coleta de dados de atletas", "main": "index.js", "scripts": {"start": "node scripts/scheduler.js", "test-world": "node tests/test-world-athletics.js", "test-olympics": "node tests/test-olympics.js", "test-db": "node tests/test-database.js", "consolidate": "node scripts/data-consolidator.js"}, "dependencies": {"@prisma/client": "^6.12.0", "mysql2": "^3.14.2", "node-cron": "^3.0.2", "prisma": "^6.12.0", "puppeteer": "^24.12.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "sequelize": "^6.37.7"}, "author": "Sportheca", "license": "ISC"}