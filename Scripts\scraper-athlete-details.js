const https = require('https');
const { URL } = require('url');
const fs = require('fs');
const path = require('path');

// URL base da API para buscar resultados detalhados dos atletas
const baseApiUrl = 'https://worldathletics.org/WorldRanking/RankingScoreCalculation';

// Configurações de processamento e controle de fluxo
const CONFIG = {
  DELAY_BETWEEN_REQUESTS: 100,
  DELAY_BETWEEN_PAGES: 1000,
  BATCH_SIZE: 10,
  MAX_PAGES: null,
  CHECKPOINT_FILE_MEN: 'athlete-details-checkpoint-men.json',
  CHECKPOINT_FILE_WOMEN: 'athlete-details-checkpoint-women.json'
};

// Cria pausa entre requisições para evitar sobrecarga do servidor
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Retorna o nome do arquivo de checkpoint baseado no gênero
function getCheckpointFile(gender) {
  return gender === 'men' ? CONFIG.CHECKPOINT_FILE_MEN : CONFIG.CHECKPOINT_FILE_WOMEN;
}

// Salva progresso atual em arquivo para permitir retomada posterior
function saveCheckpoint(data, gender) {
  try {
    const checkpointFile = getCheckpointFile(gender);
    fs.writeFileSync(checkpointFile, JSON.stringify(data, null, 2));
    console.log(`Checkpoint salvo: ${data.processedAthletes} atletas processados`);
  } catch (error) {
    console.error('Erro ao salvar checkpoint:', error.message);
  }
}

// Carrega progresso salvo anteriormente ou retorna valores padrão
function loadCheckpoint(gender) {
  try {
    const checkpointFile = getCheckpointFile(gender);
    if (fs.existsSync(checkpointFile)) {
      const data = JSON.parse(fs.readFileSync(checkpointFile, 'utf8'));
      console.log(`Checkpoint carregado: ${data.processedAthletes} atletas já processados`);
      return data;
    }
  } catch (error) {
    console.error('Erro ao carregar checkpoint:', error.message);
  }
  return { processedAthletes: 0, lastPage: 0, processedIds: [] };
}

// Remove arquivo de checkpoint para reiniciar processamento do zero
function clearCheckpoint(gender) {
  try {
    const checkpointFile = getCheckpointFile(gender);
    if (fs.existsSync(checkpointFile)) {
      fs.unlinkSync(checkpointFile);
      console.log('Checkpoint removido');
    }
  } catch (error) {
    console.error('Erro ao remover checkpoint:', error.message);
  }
}

// Faz requisições HTTP para a API do World Athletics
function fetchData(url, callback) {
  const reqUrl = new URL(url);

  const options = {
    hostname: reqUrl.hostname,
    path: reqUrl.pathname + reqUrl.search,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Node.js)',
      'Accept': 'application/json, text/html',
    },
    port: 443,
    method: 'GET',
    rejectUnauthorized: false,
  };

  const req = https.request(options, (res) => {
    res.setEncoding('utf8');
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => callback(null, data));
  });

  req.on('error', (err) => {
    callback(err, null);
  });

  req.end();
}

// Busca e processa resultados detalhados de um atleta específico
function processAthleteResults(competitorId, athleteIndex = 0, totalAthletes = 0) {
  return new Promise((resolve, reject) => {
    const apiUrl = `${baseApiUrl}?competitorId=${competitorId}`;

    fetchData(apiUrl, (err, data) => {
      if (err) {
        console.error(`Erro ao buscar dados do atleta ${competitorId}:`, err.message);
        reject(err);
        return;
      }

      try {
        // Limpa dados recebidos para facilitar parsing do JSON
        let cleanData = data.replace(/\n/g, '').replace(/\r/g, '').trim();

        // Remove escape de aspas se necessário
        if (cleanData.startsWith('"') && cleanData.endsWith('"')) {
          cleanData = cleanData.slice(1, -1);
          cleanData = cleanData.replace(/\\"/g, '"');
        }

        const athleteData = JSON.parse(cleanData);

        // Log dos resultados processados
        if (athleteData.results && athleteData.results.length > 0) {
          console.log(`Atleta ${athleteData.athlete}: ${athleteData.results.length} resultados processados`);
        }

        resolve(athleteData);

      } catch (parseError) {
        console.error(`Erro ao processar JSON do atleta ${competitorId}:`, parseError.message);
        reject(parseError);
      }
    });
  });
}

// Processa múltiplos atletas salvando resultados incrementalmente no banco
async function processMultipleAthletesWithDB(competitorIds, dbModel = null, gender = 'men') {
  console.log(`Iniciando processamento de ${competitorIds.length} atletas...`);

  const checkpoint = loadCheckpoint(gender);
  let processedCount = 0;
  let savedResults = 0;
  let errors = 0;
  let skippedCount = 0;

  for (let i = 0; i < competitorIds.length; i++) {
    const competitorId = competitorIds[i];

    // Pula atletas já processados
    if (checkpoint.processedIds.includes(competitorId)) {
      skippedCount++;
      continue;
    }

    try {
      const athleteData = await processAthleteResults(competitorId, i, competitorIds.length);

      // Salva no banco se há resultados e modelo foi fornecido
      if (athleteData && athleteData.results && athleteData.results.length > 0 && dbModel) {
        for (const result of athleteData.results) {
          try {
            await dbModel.upsert({
              athlete_id: competitorId,
              athlete_name: athleteData.athlete,
              gender: gender,
              competition_date: result.date,
              discipline: result.discipline,
              result_mark: result.mark,
              competition_name: result.competition,
              place_position: result.place
            });
            savedResults++;
          } catch (dbError) {
            console.error(`Erro ao salvar resultado no banco:`, dbError.message);
          }
        }
      }

      processedCount++;
      checkpoint.processedIds.push(competitorId);
      checkpoint.processedAthletes = checkpoint.processedAthletes + 1; // Incrementa o total geral

      // Salva checkpoint a cada 5 atletas processados
      if (processedCount % 5 === 0) {
        saveCheckpoint(checkpoint, gender);
      }

      // Delay entre atletas
      if (i < competitorIds.length - 1) {
        await delay(CONFIG.DELAY_BETWEEN_REQUESTS);
      }

    } catch (error) {
      errors++;
      console.error(`Falha ao processar atleta ${competitorId}:`, error.message);
      // Continua com o próximo atleta mesmo se houver erro
    }
  }

  // Salva checkpoint final
  saveCheckpoint(checkpoint, gender);

  console.log(`Processamento finalizado: ${processedCount} atletas processados, ${savedResults} resultados salvos`);

  return { processedCount, savedResults, errors, skippedCount };
}

// Extrai IDs dos atletas de uma página do ranking
function processPage(html, pageNumber) {
  console.log(`Processando página ${pageNumber}...`);

  const content0 = substringFrom('<div class="table-wrapper">', '<!-- Search modal this one-->', html);
  const content1 = substringFrom('<tbody>', '</tbody>', content0);

  // Se não encontrar tbody, significa que não há mais dados
  if (!content1) {
    console.log(`Página ${pageNumber}: Nenhum dado encontrado (fim das páginas)`);
    return []; // Retorna array vazio
  }

  const rows = content1.split('</tr>');
  const athleteIds = [];

  rows.forEach((element) => {
    // Extrai apenas o ID do atleta (mesma lógica do script 1)
    const athleteID = substringFrom('data-id="', '"', element);
    if (athleteID) {
      athleteIds.push(athleteID);
    }
  });

  console.log(`Página ${pageNumber}: ${athleteIds.length} atletas encontrados`);

  return athleteIds;
}

// Processa páginas do ranking coletando detalhes dos atletas com controle de limite
async function scrapeAllPagesImproved(gender = 'men', maxPages = null, dbModel = null) {
  const startTime = new Date();
  console.log(`Iniciando processamento às ${startTime.toLocaleString()}`);

  // Valida o parâmetro de gênero
  if (gender !== 'men' && gender !== 'women') {
    console.error('❌ Gênero inválido. Use "men" ou "women".');
    return;
  }

  // Carrega checkpoint
  const checkpoint = loadCheckpoint(gender);

  // Constrói a URL base específica para o gênero
  const baseUrl = `https://worldathletics.org/world-rankings/overall-ranking/${gender}`;

  let currentPage = Math.max(1, checkpoint.lastPage + 1); // Retoma da última página processada
  let hasMorePages = true;
  let totalProcessedInThisRun = 0; // Conta apenas desta execução
  let totalProcessedOverall = checkpoint.processedAthletes; // Total geral do checkpoint

  const limitText = maxPages ? `(máximo ${maxPages} páginas)` : '(todas as páginas)';
  console.log(`Iniciando coleta do ranking ${gender === 'men' ? 'MASCULINO' : 'FEMININO'} ${limitText}`);
  console.log(`Iniciando da página: ${currentPage}`);
  if (totalProcessedOverall > 0) {
    console.log(`Atletas já processados: ${totalProcessedOverall}`);
  }

  while (hasMorePages) {
    // Verifica limite de páginas
    if (maxPages && currentPage > maxPages) {
      console.log(`Limite de ${maxPages} páginas atingido.`);
      break;
    }

    // Constrói a URL da página atual
    const pageUrl = `${baseUrl}?page=${currentPage}`;

    try {
      // Faz a requisição HTTP
      const html = await new Promise((resolve, reject) => {
        fetchData(pageUrl, (err, html) => {
          if (err) reject(err);
          else resolve(html);
        });
      });

      // Processa a página atual e extrai os IDs dos atletas
      const pageIds = processPage(html, currentPage);

      // Se não há mais dados, para o loop
      if (pageIds.length === 0) {
        hasMorePages = false;
        console.log('Não há mais páginas com dados.');
      } else {
        // Processa os atletas desta página imediatamente
        const stats = await processMultipleAthletesWithDB(pageIds, dbModel, gender);

        totalProcessedInThisRun += stats.processedCount;
        totalProcessedOverall += stats.processedCount;

        // Atualiza checkpoint com a página processada
        checkpoint.lastPage = currentPage;
        checkpoint.processedAthletes = totalProcessedOverall;
        saveCheckpoint(checkpoint, gender);

        console.log(`Página ${currentPage} concluída: ${stats.processedCount} atletas processados`);
      }

      // Se ainda há mais páginas, aguarda antes da próxima
      if (hasMorePages) {
        await delay(CONFIG.DELAY_BETWEEN_PAGES);
      }

      currentPage++;

    } catch (error) {
      console.error(`Erro na página ${currentPage}:`, error.message);
      hasMorePages = false;
    }
  }

  const endTime = new Date();
  const durationMs = endTime - startTime;
  const durationMinutes = Math.round(durationMs / 1000 / 60);
  const durationSeconds = Math.round((durationMs % 60000) / 1000);

  console.log(`Processamento concluído: ${totalProcessedInThisRun} atletas processados nesta execução`);
  console.log(`Total geral: ${totalProcessedOverall} atletas`);
  console.log(`Tempo de execução: ${durationMinutes}m ${durationSeconds}s`);
  console.log(`Finalizado às ${endTime.toLocaleString()}`);

  return totalProcessedInThisRun; // Retorna apenas os processados nesta execução
}



// Extrai texto entre duas strings delimitadoras
function substringFrom(start, end, text) {
  const startIndex = text.indexOf(start);
  const endIndex = text.indexOf(end, startIndex + start.length);
  if (startIndex === -1 || endIndex === -1) {
    return null;
  }
  return text.substring(startIndex + start.length, endIndex).trim();
}

// Função principal para coletar detalhes dos atletas com opções configuráveis
async function scrapeAthleteDetails(gender = 'men', options = {}) {
  const {
    maxPages = CONFIG.MAX_PAGES,           // null = todas as páginas
    dbModel = null,                        // Modelo do banco para salvar
    clearPreviousProgress = false          // Se deve limpar progresso anterior
  } = options;

  console.log(`Iniciando scraper de detalhes dos atletas - ${gender === 'men' ? 'Masculino' : 'Feminino'}`);
  if (maxPages) {
    console.log(`Limite: ${maxPages} páginas`);
  }

  if (clearPreviousProgress) {
    console.log('Limpando progresso anterior...');
    clearCheckpoint(gender);
  }

  try {
    // Usa a função melhorada que processa página por página
    const totalProcessed = await scrapeAllPagesImproved(gender, maxPages, dbModel);

    console.log(`Processamento concluído com sucesso: ${totalProcessed} atletas processados`);

    if (!dbModel) {
      console.log('Nota: Dados não foram salvos no banco (dbModel não fornecido)');
    }

    return { success: true, totalProcessed };

  } catch (error) {
    console.error('Erro durante o processamento:', error.message);
    console.log('Progresso foi salvo em checkpoint - você pode retomar a execução');
    return { success: false, error: error.message };
  }
}

// Processa apenas um número limitado de páginas
async function scrapeAthleteDetailsLimited(gender = 'men', maxPages = 10, dbModel = null) {
  return scrapeAthleteDetails(gender, { maxPages, dbModel });
}

// Retoma processamento a partir do último checkpoint salvo
async function resumeAthleteDetails(gender = 'men', dbModel = null) {
  return scrapeAthleteDetails(gender, { dbModel, clearPreviousProgress: false });
}

// Retoma processamento com limite adicional de páginas a partir do checkpoint
async function resumeAthleteDetailsWithLimit(gender = 'men', additionalPages = 10, dbModel = null) {
  // Carrega checkpoint para saber onde parou
  const checkpoint = loadCheckpoint(gender);
  const startPage = checkpoint.lastPage + 1;
  const endPage = startPage + additionalPages - 1;

  console.log(`Retomando da página ${startPage} até página ${endPage} (${additionalPages} páginas)`);

  try {
    // Modifica a função melhorada para aceitar range de páginas
    const totalProcessed = await scrapeAllPagesImprovedWithRange(gender, startPage, endPage, dbModel);
    return { success: true, totalProcessed };
  } catch (error) {
    console.error('Erro durante o processamento:', error.message);
    return { success: false, error: error.message };
  }
}

// Processa um intervalo específico de páginas do ranking
async function scrapeAllPagesImprovedWithRange(gender = 'men', startPage = 1, endPage = null, dbModel = null) {
  const startTime = new Date();
  console.log(`Iniciando processamento às ${startTime.toLocaleString()}`);

  // Valida o parâmetro de gênero
  if (gender !== 'men' && gender !== 'women') {
    console.error('Gênero inválido. Use "men" ou "women".');
    return 0;
  }

  // Carrega checkpoint
  const checkpoint = loadCheckpoint(gender);

  // Constrói a URL base específica para o gênero
  const baseUrl = `https://worldathletics.org/world-rankings/overall-ranking/${gender}`;

  let currentPage = startPage;
  let hasMorePages = true;
  let totalProcessedInThisRun = 0;
  let totalProcessedOverall = checkpoint.processedAthletes;

  console.log(`Processando páginas ${startPage} até ${endPage || 'fim'} do ranking ${gender === 'men' ? 'MASCULINO' : 'FEMININO'}`);

  while (hasMorePages) {
    // Verifica limite de páginas
    if (endPage && currentPage > endPage) {
      console.log(`Limite da página ${endPage} atingido.`);
      break;
    }

    // Constrói a URL da página atual
    const pageUrl = `${baseUrl}?page=${currentPage}`;

    try {
      // Faz a requisição HTTP
      const html = await new Promise((resolve, reject) => {
        fetchData(pageUrl, (err, html) => {
          if (err) reject(err);
          else resolve(html);
        });
      });

      // Processa a página atual e extrai os IDs dos atletas
      const pageIds = processPage(html, currentPage);

      // Se não há mais dados, para o loop
      if (pageIds.length === 0) {
        hasMorePages = false;
        console.log('Não há mais páginas com dados.');
      } else {
        // Processa os atletas desta página imediatamente
        const stats = await processMultipleAthletesWithDB(pageIds, dbModel, gender);

        totalProcessedInThisRun += stats.processedCount;
        totalProcessedOverall += stats.processedCount;

        // Atualiza checkpoint com a página processada
        checkpoint.lastPage = currentPage;
        checkpoint.processedAthletes = totalProcessedOverall;
        saveCheckpoint(checkpoint, gender);

        console.log(`Página ${currentPage} concluída: ${stats.processedCount} atletas processados`);
      }

      // Se ainda há mais páginas, aguarda antes da próxima
      if (hasMorePages) {
        await delay(CONFIG.DELAY_BETWEEN_PAGES);
      }

      currentPage++;

    } catch (error) {
      console.error(`Erro na página ${currentPage}:`, error.message);
      hasMorePages = false;
    }
  }

  const endTime = new Date();
  const durationMs = endTime - startTime;
  const durationMinutes = Math.round(durationMs / 1000 / 60);
  const durationSeconds = Math.round((durationMs % 60000) / 1000);

  console.log(`Processamento concluído: ${totalProcessedInThisRun} atletas processados nesta execução`);
  console.log(`Total geral: ${totalProcessedOverall} atletas`);
  console.log(`Tempo de execução: ${durationMinutes}m ${durationSeconds}s`);
  console.log(`Finalizado às ${endTime.toLocaleString()}`);

  return totalProcessedInThisRun;
}

// Exporta as funções principais
module.exports = {
  scrapeAthleteDetails,
  scrapeAthleteDetailsLimited,
  resumeAthleteDetails,
  resumeAthleteDetailsWithLimit,
  clearCheckpoint,
  CONFIG
};

