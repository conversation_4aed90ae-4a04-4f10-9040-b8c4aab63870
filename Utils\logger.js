const fs = require('fs');
const path = require('path');

class Logger { // Corrigido para "Logger" com "L" maiúsculo
  constructor() {
    this.logPath = process.env.LOG_FILE_PATH || './logs/'; // Corrigido de "-" para "="
    this.logLevel = process.env.LOG_LEVEL || 'info';
    this.ensureLogDirectory();
  }

  // Garante que o diretório de logs existe
  ensureLogDirectory() {
    if (!fs.existsSync(this.logPath)) {
      fs.mkdirSync(this.logPath, { recursive: true });
    }
  }

  // Método principal de log
  log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      data: data || undefined
    };

    // Log no console
    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }

    // Log no arquivo
    this.writeToFile(logEntry);
  }

  // Escreve no arquivo de log
  writeToFile(logEntry) {
    const today = new Date().toISOString().split('T')[0];
    const logFile = path.join(this.logPath, `${today}.log`);
    const logLine = JSON.stringify(logEntry) + '\n';

    fs.appendFileSync(logFile, logLine, 'utf8');
  }

  // Métodos de conveniência
  info(message, data = null) {
    this.log('info', message, data);
  }

  warn(message, data = null) {
    this.log('warn', message, data);
  }

  error(message, data = null) {
    this.log('error', message, data);
  }

  debug(message, data = null) {
    if (this.logLevel === 'debug') {
      this.log('debug', message, data);
    }
  }

  // Log específico para início de operações
  startOperation(operationName) {
    this.info(`Iniciando operação: ${operationName}`);
  }

  // Log específico para fim de operações
  endOperation(operationName, duration = null) {
    const message = duration ?
      `Operação concluída: ${operationName} (${duration}ms)` :
      `Operação concluída: ${operationName}`;
    this.info(message);
  }

  // Log específico para erros de operações
  operationError(operationName, error) {
    this.error(`Erro na operação: ${operationName}`, {
      error: error.message,
      stack: error.stack
    });
  }
}

module.exports = new Logger(); // Exporta a instância da classe "Logger"