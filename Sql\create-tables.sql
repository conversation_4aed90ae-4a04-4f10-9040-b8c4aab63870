-- Criar banco de dados
CREATE DATABASE IF NOT EXISTS sportheca_athletes;
USE sportheca_athletes;

-- Tabela World Athletics
CREATE TABLE IF NOT EXISTS world_athletics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    athlete_id VARCHAR(255),                   -- ID do atleta (ex: '112990305')
    athlete_position VARCHAR(255),            -- <PERSON><PERSON><PERSON> no ranking (ex: '100')
    athlete_name VARCHAR(255) NOT NULL,       -- Nome do atleta
    birth_date VARCHAR(255),                   -- Data de nascimento (ex: '28 JAN 1993')
    country VARCHAR(255),                      -- <PERSON><PERSON> (ex: 'nigeria')
    score VARCHAR(255),                        -- Score (ex: '1374')
    event VARCHAR(255),                        -- Evento (ex: 'Shot Put [Shot Put]')
    athlete_url VARCHAR(255),                  -- URL do perfil (ex: '/athletes/nigeria/...')
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Índices para performance
    INDEX idx_athlete_id (athlete_id),
    INDEX idx_athlete_name (athlete_name),
    INDEX idx_country (country),
    INDEX idx_event (event)
);

-- Tabela Olympics
CREATE TABLE IF NOT EXISTS olympics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    athlete_name VARCHAR(255) NOT NULL,
    is_olympic BOOLEAN DEFAULT FALSE,
    olympic_medals TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_athlete_name (athlete_name)
);

-- Tabela Test Results (resultados detalhados das provas)
CREATE TABLE IF NOT EXISTS test_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    athlete_id VARCHAR(255),                   -- ID do atleta (ex: '0113033816')
    athlete_name VARCHAR(255) NOT NULL,       -- Nome do atleta
    competition_date VARCHAR(255),            -- Data da prova
    discipline VARCHAR(255),                   -- Evento/disciplina (ex: '5000 Metres')
    result_mark VARCHAR(255),                  -- Resultado (ex: '14:28.56', '9.79')
    competition_name VARCHAR(255),            -- Nome da competição
    place_position VARCHAR(255),              -- Colocação (ex: '1', '2', 'DNF')
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Índices para performance
    INDEX idx_athlete_id (athlete_id),
    INDEX idx_athlete_name (athlete_name),
    INDEX idx_competition_date (competition_date),
    INDEX idx_discipline (discipline)
);

-- Tabela Unificada
CREATE TABLE IF NOT EXISTS unified_athletes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    athlete_name VARCHAR(255) NOT NULL,
    world_ranking INT,
    birth_date DATE,
    score DECIMAL(10,2),
    event VARCHAR(255),
    category VARCHAR(255),
    is_olympic BOOLEAN DEFAULT FALSE,
    olympic_medals TEXT,
    instagram_followers INT,
    source VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_athlete_name (athlete_name)
);

-- Tabela Instagram
CREATE TABLE IF NOT EXISTS instagram (
    id INT AUTO_INCREMENT PRIMARY KEY,
    athlete_name VARCHAR(255) NOT NULL,
    instagram_followers INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_athlete_name (athlete_name)
);

-- Tabela Histórico
CREATE TABLE IF NOT EXISTS athlete_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    athlete_name VARCHAR(255) NOT NULL,
    changes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (athlete_name) REFERENCES unified_athletes(athlete_name)
);

-- Índices para otimização
CREATE INDEX idx_olympics_athlete_name ON olympics(athlete_name);
CREATE INDEX idx_test_results_athlete_id ON test_results(athlete_id);
CREATE INDEX idx_test_results_athlete_name ON test_results(athlete_name);
CREATE INDEX idx_test_results_date ON test_results(competition_date);
CREATE INDEX idx_unified_athletes_athlete_name ON unified_athletes(athlete_name);
CREATE INDEX idx_instagram_athlete_name ON instagram(athlete_name);
CREATE INDEX idx_history_athlete_name ON athlete_history(athlete_name);
CREATE INDEX idx_history_created_at ON athlete_history(created_at);