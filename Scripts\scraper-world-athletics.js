const https = require('https');
const { URL } = require('url');

// Converte entidades HTML codificadas para caracteres legíveis
function decodeHtmlEntities(text) {
  if (!text) return text;

  return text
    .replace(/&#(\d+);/g, (match, dec) => String.fromCharCode(dec))
    .replace(/&#x([0-9a-f]+);/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'")
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&');
}

// URL base para acessar o ranking mundial de atletismo
const baseUrlTemplate = 'https://worldathletics.org/world-rankings/overall-ranking';

// Cria pausa entre requisições para evitar sobrecarga do servidor
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Faz requisição HTTP e retorna o HTML da página
function fetchHTML(url, callback) {
  const reqUrl = new URL(url);

  const options = {
    hostname: reqUrl.hostname,
    path: reqUrl.pathname + reqUrl.search,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Node.js)',
      'Accept': 'text/html',
    },
    port: 443,
    method: 'GET',
    rejectUnauthorized: false,
  };

  const req = https.request(options, (res) => {
    res.setEncoding('utf8');
    let html = '';
    res.on('data', chunk => html += chunk);
    res.on('end', () => callback(null, html));
  });

  req.on('error', (err) => {
    callback(err, null);
  });

  req.end();
}

// Extrai texto entre duas strings delimitadoras
function substringFrom(start, end, text) {
  const startIndex = text.indexOf(start);
  const endIndex = text.indexOf(end, startIndex + start.length);
  if (startIndex === -1 || endIndex === -1) {
    return null;
  }
  return text.substring(startIndex + start.length, endIndex).trim();
}

// Extrai nacionalidade do atleta a partir do HTML da célula
function extractNationality(text) {
  const imgEnd = text.indexOf('</img>') + 6;
  const textStart = text.indexOf('>', imgEnd) + 1;
  const textEnd = text.indexOf('</', textStart);
  return textStart !== -1 && textEnd !== -1 ? text.slice(textStart, textEnd).trim() : null;
}

// Processa uma página do ranking e extrai dados dos atletas
function processPage(html, pageNumber, athletesArr, gender) {
  console.log(`\n=== Processando página ${pageNumber} ===`);

  const content0 = substringFrom('<div class="table-wrapper">', '<!-- Search modal this one-->', html);
  const content1 = substringFrom('<tbody>', '</tbody>', content0);

  // Verifica se ainda há dados para processar
  if (!content1) {
    console.log(`Página ${pageNumber}: Nenhum dado encontrado (fim das páginas)`);
    return false;
  }

  const rows = content1.split('</tr>');
  let athletesProcessed = 0;

  rows.forEach(element => {
    // Extrai informações de cada atleta do HTML
    const athleteID = substringFrom('data-id="', '"', element);
    const athletePosition = substringFrom ('<td data-th="Rank">', '</td>', element);
    const athleteName = substringFrom('<td data-th="Competitor">', '</td>', element);
    const athleteDateOfBirth = substringFrom('<td data-th="DOB">', '</td>', element);
    const athleteCountry = substringFrom(' data-athlete-url="/athletes/', '/', element);
    const athleteScore = substringFrom('<td data-th="score">', '</td>', element);
    const athleteEvent = substringFrom('<td data-th="EventList">', '</td>', element)
    const athleteUrl = substringFrom('data-athlete-url="', '"', element);

    // Processa apenas registros com dados válidos
    if (athleteID) {
      const decodedName = decodeHtmlEntities(athleteName);

      console.log('\n\n-----')
      console.log('ID: ', athleteID);
      console.log('Posição:', athletePosition);
      console.log('Nome: ', decodedName);
      console.log('Data de Nascimento: ', athleteDateOfBirth);
      console.log('País:', athleteCountry);
      console.log('Score: ', athleteScore);
      console.log('Evento:', athleteEvent);
      console.log('URL perfil do atleta: ', athleteUrl);

      athletesArr.push({
        athlete_id: athleteID,
        gender: gender,
        athlete_position: athletePosition,
        athlete_name: decodedName,
        birth_date: athleteDateOfBirth,
        country: athleteCountry,
        score: athleteScore,
        event: athleteEvent,
        athlete_url: athleteUrl
      });
      athletesProcessed++;
    }
  });

  console.log(`\nPágina ${pageNumber}: ${athletesProcessed} atletas processados`);
  return true;
}

// Coleta dados do ranking mundial de atletismo para o gênero especificado
async function scrapeWorldAthletics(gender = 'men') {
  if (gender !== 'men' && gender !== 'women') throw new Error('Gênero inválido. Use "men" ou "women".');

  const baseUrl = `${baseUrlTemplate}/${gender}`;

  let currentPage = 1;
  let hasMorePages = true;
  const athletes = [];


  console.log(`Iniciando scraping do ranking ${gender === 'men' ? 'MASCULINO' : 'FEMININO'}...\n`);
  console.log(`URL base: ${baseUrl}\n`);

  // Processa todas as páginas disponíveis do ranking
  while (hasMorePages) {
    console.log(`Processando página ${currentPage}...`);

    const pageUrl = `${baseUrl}?page=${currentPage}`;
    console.log(`URL: ${pageUrl}`);

    // Obtém HTML da página atual
    const html = await new Promise((resolve, reject) => {
      fetchHTML(pageUrl, (err, html) => {
        if (err) reject(err);
        else resolve(html);
      });
    });

    console.log(`HTML recebido: ${html.length} caracteres`);

    const hasData = processPage(html, currentPage, athletes, gender);

    // Para o loop quando não há mais dados
    if (!hasData) {
      hasMorePages = false;
      console.log('Não há mais páginas com dados.');
    }

    // Aguarda antes de processar próxima página
    if (hasMorePages) {
      console.log(`\nAguardando 2 segundos antes da próxima página...\n`);
      await delay(2000);
    }

    currentPage++;
  }

  console.log(` Scraping do ranking ${gender === 'men' ? 'MASCULINO' : 'FEMININO'} concluído! Total de páginas processadas: ${currentPage - 1}`);
  return athletes;
}



// Processa rankings de ambos os gêneros em sequência
async function scrapeAllGenders() {
  console.log('Iniciando scraping completo - MASCULINO e FEMININO\n');
  console.log('=' .repeat(60));

  try {
    await scrapeWorldAthletics('men');

    console.log('\n' + '=' .repeat(60));
    console.log('Aguardando 5 segundos antes de processar o ranking feminino...\n');
    await delay(5000);

    await scrapeWorldAthletics('women');

    console.log('\n' + '=' .repeat(60));
    console.log('SCRAPING FINALIZADO! Ambos os rankings foram processados com sucesso.');

  } catch (error) {
    console.error('Erro durante o scraping completo:', error.message);
  }
}

module.exports = { scrapeWorldAthletics };