module.exports = (sequelize, DataTypes) => {
  const UnifiedAthletes = sequelize.define('UnifiedAthletes', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    athlete_id: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'ID do atleta da tabela world_athletes'
    },
    gender: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Gênero do atleta (masculino ou feminino)'
    },
    athlete_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'Nome do atleta da tabela world_athletes'
    },
    age: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Idade calculada a partir de birth_date ou vazio se não disponível'
    },
    country: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'País do atleta da tabela world_athletes'
    },
    world_score: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Score do atleta da tabela world_athletes (renomeado de score)'
    },
    event: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Evento específico do atleta (ex: "1500m")'
    },
    result_mark: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Resultado do atleta neste evento específico (ex: "3:30.37")'
    },
    resultado_centesimos_seg: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Resultado convertido para centésimos de segundo para facilitar comparações (ex: 21037.00 para 3:30.37)'
    },
    calculation_score: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Score calculado customizado para ranking e análises (será implementado posteriormente)'
    },
    competition_date: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Data da competição deste resultado específico'
    },
    instagram_followers: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Número de seguidores no Instagram do atleta'
    },
    is_olympic_athlete: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
      comment: 'Indica se o atleta é olímpico (verificado nas tabelas olympics_individual e olympics_team)'
    },
    has_olympic_medal: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
      comment: 'Indica se o atleta possui medalha olímpica (posição 1, 2 ou 3)'
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'unified_athletes',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['athlete_id', 'event']
      },
      {
        fields: ['athlete_name']
      },
      {
        fields: ['country']
      },
      {
        fields: ['is_olympic_athlete']
      },
      {
        fields: ['has_olympic_medal']
      },
      {
        fields: ['resultado_centesimos_seg']
      },
      {
        fields: ['calculation_score']
      }
    ]
  });
  return UnifiedAthletes;
};