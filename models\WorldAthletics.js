module.exports = (sequelize, DataTypes) => {
  const WorldAthletics = sequelize.define('WorldAthletics', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    athlete_id: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    gender: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    athlete_position: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    athlete_name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    birth_date: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    country: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    score: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    event: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    athlete_url: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'world_athletics',
    timestamps: false
  });
  return WorldAthletics;
}; 