module.exports = (sequelize, DataTypes) => {
  const AthleteHistory = sequelize.define('AthleteHistory', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    athlete_name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    changes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'athlete_history',
    timestamps: false
  });

  // Relacionamento 
  AthleteHistory.associate = (models) => {
    AthleteHistory.belongsTo(models.UnifiedAthletes, {
      foreignKey: 'athlete_name',
      targetKey: 'athlete_name',
      as: 'unifiedAthlete'
    });
  };

  return AthleteHistory;
}; 