const logger = require('./logger');

class HtmlParser { //classe que vai buscar informações no html
  constructor() {
    this.currentHtml = '';
  }

  // Carrega o HTML para análise
  loadHtml(html) {
    this.currentHtml = html;
    return this;
  }

  // Extrai texto entre tags específicas
  extractTextBetweenTags(tagName, className = null) {
    try {
      let pattern;
      
      if (className) {
        // Busca por tag com classe específica
        pattern = new RegExp(`<${tagName}[^>]*class=["'][^"']*${className}[^"']*["'][^>]*>(.*?)<\/${tagName}>`, 'gis');
      } else {
        // Busca por tag simples
        pattern = new RegExp(`<${tagName}[^>]*>(.*?)<\/${tagName}>`, 'gis');
      }

      //guarda informações importantes e limpa qualquer tag html
      const matches = [];
      let match;
      
      while ((match = pattern.exec(this.currentHtml)) !== null) {
        matches.push(this.cleanText(match[1]));
      }

      return matches; //devolve a lista com as informações entre tags, 
    } catch (error) {
      logger.error('Erro ao extrair texto entre tags:', error); //caso de erro, retona aviso
      return [];
    }
  }

  // Extrai atributos de elementos
  extractAttribute(tagName, attributeName, className = null) {
    try {
      let pattern;
      
      if (className) {
        pattern = new RegExp(`<${tagName}[^>]*class=["'][^"']*${className}[^"']*["'][^>]*${attributeName}=["']([^"']*)["'][^>]*>`, 'gis');
      } else {
        pattern = new RegExp(`<${tagName}[^>]*${attributeName}=["']([^"']*)["'][^>]*>`, 'gis');
      }

      const matches = [];
      let match;
      
      while ((match = pattern.exec(this.currentHtml)) !== null) {
        matches.push(match[1]);
      }

      return matches;
    } catch (error) {
      logger.error('Erro ao extrair atributo:', error);
      return [];
    }
  }

  // Busca por padrões específicos no HTML
  findPattern(pattern) {
    try {
      const regex = new RegExp(pattern, 'gis');
      const matches = [];
      let match;
      
      while ((match = regex.exec(this.currentHtml)) !== null) {
        matches.push(match[1] || match[0]);
      }

      return matches;
    } catch (error) {
      logger.error('Erro ao buscar padrão:', error);
      return [];
    }
  }

  // Limpa texto removendo tags HTML e espaços extras
  cleanText(text) {
    if (!text) return '';
    
    return text
      .replace(/<[^>]*>/g, '') // Remove tags HTML
      .replace(/&nbsp;/g, ' ') // Substitui &nbsp; por espaço
      .replace(/&amp;/g, '&') // Substitui &amp; por &
      .replace(/&lt;/g, '<') // Substitui &lt; por <
      .replace(/&gt;/g, '>') // Substitui &gt; por >
      .replace(/&quot;/g, '"') // Substitui &quot; por "
      .replace(/&#39;/g, "'") // Substitui &#39; por '
      .replace(/\s+/g, ' ') // Substitui múltiplos espaços por um
      .trim(); // Remove espaços do início e fim
  }

  // Extrai tabelas do HTML
  extractTable(tableClass = null) {
    try {
      let tablePattern;
      
      if (tableClass) {
        tablePattern = new RegExp(`<table[^>]*class=["'][^"']*${tableClass}[^"']*["'][^>]*>(.*?)<\/table>`, 'gis');
      } else {
        tablePattern = new RegExp(`<table[^>]*>(.*?)<\/table>`, 'gis');
      }

      const tableMatch = tablePattern.exec(this.currentHtml);
      if (!tableMatch) return [];

      const tableContent = tableMatch[1];
      const rowPattern = /<tr[^>]*>(.*?)<\/tr>/gis;
      const rows = [];
      let rowMatch;

      while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
        const cellPattern = /<t[dh][^>]*>(.*?)<\/t[dh]>/gis;
        const cells = [];
        let cellMatch;

        while ((cellMatch = cellPattern.exec(rowMatch[1])) !== null) {
          cells.push(this.cleanText(cellMatch[1]));
        }

        if (cells.length > 0) {
          rows.push(cells);
        }
      }

      return rows;
    } catch (error) {
      logger.error('Erro ao extrair tabela:', error);
      return [];
    }
  }
}

module.exports = HtmlParser;