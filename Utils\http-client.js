//Ferramentas do node.js para fazer requisições
const https = require('https');
const http = require('http');
const {url} = require('url');
const logger = require('./logger');

//classe que faz configurações importantes sobre as requisições
class HttpClient{
    constructor(options = {}){ //configurando algumas regras
        this.timeout = options.timeout || 10000;
        this.maxRetries = options.maxRetires || 3; 
        this.delay = options.delay || 2000;
    }

// recebe endereço da fonte para saber onde vai fazer a requisição
async request(url, options = {}){
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';

// informações importantes para fazer a requisição
    const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            ...options.headers
        }
    };
    //função para tentar de novo caso de erro
    return this.makeRequestWichRetry(url, requestOptions, isHttps);
}
// Aplicando o método retry
async makeRequestWichRetry(url, requestOptions, isHttps, attempt = 1){
    try {
      logger.info(`Tentativa ${attempt} para ${url}`);
      const response = await this.makeRequest(requestOptions, isHttps);
      logger.info(`Sucesso na requisição para ${url}`); // caso de sucesso
      return response;
    } catch (error) {
      if (attempt < this.maxRetries) { 
        logger.warn(`Erro na tentativa ${attempt} para ${url}. Tentando novamente em ${this.delay}ms...`);
        await this.sleep(this.delay);
        return this.makeRequestWichRetry(url, requestOptions, isHttps, attempt + 1);
      }
      logger.error(`Falha após ${this.maxRetries} tentativas para ${url}:`, error.message);
      throw error; // caso de erro após 3 tentativas 
    }
  }
makeRequest(options, isHttps) { //fazendo a requisição usando HTTP ou HTTPS
    return new Promise((resolve, reject) => {
      const client = isHttps ? https : http;
      
      const req = client.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              body: data
            });
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.setTimeout(this.timeout, () => { // caso a requisição demore mais que 10 segundos, a requisição é interrompida
        req.abort();
        reject(new Error('Request timeout'));
      });

      req.end(); // Finaliza a requisição
    });
  }

  // Método utilitário para delay
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = HttpClient;



