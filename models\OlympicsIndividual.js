module.exports = (sequelize, DataTypes) => {
  const OlympicsIndividual = sequelize.define('OlympicsIndividual', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    athlete_name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    event: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    country: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    result: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    position: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    tableName: 'olympics_individual',
    timestamps: false
  });
  return OlympicsIndividual;
}; 