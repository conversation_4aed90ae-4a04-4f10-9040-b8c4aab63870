# SPORTHECA - Automação de Coleta de Dados de Atletas de Atletismo

## Índice

1. [Introdução](#1-introdução)
2. [Briefing](#2-briefing)
   - 2.1. [Contexto do Projeto](#21-contexto-do-projeto)
   - 2.2. [<PERSON><PERSON>/Desafio](#22-problem<PERSON><PERSON><PERSON>)
   - 2.3. [Ob<PERSON><PERSON> Principal](#23-objetivo-principal)
   - 2.4. [Planejamento Inicial](#24-planejamento-inicial)
3. [Estrutura de Dados (Tabelas do Banco)](#3-estrutura-de-dados-tabelas-do-banco)
   - 3.1. [unified_athletes](#31-unified_athletes)
   - 3.2. [olympics_individual](#32-olympics_individual)
   - 3.3. [olympics_equipe](#33-olympics_equipe)
   - 3.4. [world_athletics](#34-world_athletics)
   - 3.5. [test_results](#35-test_results)
4. [Desenvolvimento (Arquivos e Scripts)](#4-desenvolvimento-arquivos-e-scripts)
   - 4.1. [.env](#41-env)
   - 4.2. [scripts/](#42-scripts)
   - 4.3. [core.js](#43-corejs)
   - 4.4. [models/](#44-models)
   - 4.5. [utils/](#45-utils)
5. [Próximos Passos](#5-próximos-passos)

---

## 1. Introdução
### 1.1. Contexto do Projeto
A Sportheca desenvolve soluções tecnológicas para o mercado esportivo. Nesse contexto, a VIB3 é uma plataforma que conecta fãs, atletas e investidores, oferecendo visibilidade e apoio financeiro a talentos com alto potencial. Para que isso funcione, é importante ter dados atualizados, confiáveis e estruturados sobre os atletas.

O foco do projeto foi a modalidade de atletismo, buscando automatizar a coleta, unificação e análise desses dados, que antes eram extraídos manualmente de sites como o World Athletics e o Olympics.com.

### 1.2. Problema/Desafio
O cenário inicial apresentava vários desafios:

- A coleta era manual (copiar e colar), lenta e sujeita a erros.
- Os dados estavam fragmentados entre plataformas e sem padronização.

- O processo demandava muito tempo da equipe e resultava em informações rapidamente desatualizadas.

### 1.3. Objetivo Principal
Desenvolver um sistema que:

- Automatize a coleta de dados de atletas de atletismo por meio de web scraping.
- Padronize e unifique essas informações em uma base estruturada.
- Calcule scores de performance com base em benchmarks olímpicos.


### 1.4. Planejamento e Etapas Realizadas
O projeto foi dividido em etapas bem definidas:

- **Coleta de Dados**: Desenvolvimento de scripts de web scraping para as plataformas World Athletics e Olympics.
- **Processamento e Consolidação**: Normalização dos dados e unificação em uma estrutura única.
- **Armazenamento**: Inserção dos dados em banco MySQL.
- **Cálculo de Métricas**: Implementação de lógica para gerar scores de desempenho, considerando benchmarks como medalhas olímpicas.

---

## 2. Estrutura de Dados (Tabelas do Banco)

### 2.1. unified_athletes

**Descrição**: Tabela principal que unifica todos os dados dos atletas.

**Origem**: Combinação de dados de `world_athletics`, `test_results` e `olympics_individual/olympics_equipe`.

**Campos Principais**:
- `athlete_id`: Identificador único do atleta
- `gender`: Gênero do atleta (men/women)
- `athlete_name`: Nome completo do atleta
- `age`: Idade calculada automaticamente
- `country`: País de origem
- `world_score`: Score do atleta da World Athletics
- `event`: Evento/disciplina específica
- `result_mark`: Melhor marca do atleta no evento
- `resultado_centesimos_seg`: Tempo convertido para centésimos de segundo
- `competition_date`: Data da competição deste resultado específico
- `is_olympic_athlete`: Indica se participou das olimpíadas
- `has_olympic_medal`: Indica se possui medalha olímpica
- `calculation_score`: Score de performance calculado
- `instagram_followers`: Número de seguidores no Instagram

**Relacionamentos**: Tabela central que agrega informações de todas as outras tabelas.

### 2.2. olympics_individual

**Descrição**: Dados de atletas que participaram das olimpíadas em eventos individuais.

**Origem**: Web scraping do site oficial das Olimpíadas.

**Campos Principais**:
- `athlete_name`: Nome do atleta
- `event`: Evento olímpico específico (ex: "100m Masculino")
- `result`: Resultado/tempo obtido
- `position`: Posição final (1º, 2º, 3º, etc.)
- `country`: País representado

**Relacionamentos**: Referenciada por `unified_athletes` para identificar atletas olímpicos e calcular scores.

### 2.3. olympics_equipe

**Descrição**: Dados de atletas que participaram das olimpíadas em eventos de equipe.

**Origem**: Web scraping do site oficial das Olimpíadas.

**Campos Principais**:
- `equipe`: Nomes dos atletas da equipe
- `event`: Evento olímpico de equipe (ex: "Revezamento 4x100m")
- `result`: Resultado/tempo da equipe
- `position`: Posição final da equipe
- `country`: País da equipe

**Relacionamentos**: Complementa `olympics_individual` para eventos de equipe.

### 2.4. world_athletics

**Descrição**: Dados de atletas do ranking mundial da World Athletics.

**Origem**: Web scraping do site oficial da World Athletics.

**Campos Principais**:
- `athlete_id`: Identificador único do atleta
- `gender`: Gênero do atleta (men/women)
- `athlete_position`: Posição no ranking mundial
- `athlete_name`: Nome do atleta
- `birth_date`: Data de nascimento do atleta
- `country`: País de origem
- `score`: Score/pontuação do atleta
- `event`: Disciplina/evento
- `athlete_url`: URL do perfil do atleta

**Relacionamentos**: Fonte primária de dados para `unified_athletes`.

### 2.5. test_results

**Descrição**: Dados complementares de resultados detalhados de atletas.

**Origem**: Web scraping do site oficial da World Athletics (detalhes dos atletas).

**Campos Principais**:
- `athlete_id`: Identificador único do atleta
- `athlete_name`: Nome do atleta
- `competition_date`: Data da competição
- `discipline`: Disciplina/evento específico
- `result_mark`: Resultado obtido
- `competition_name`: Nome da competição
- `place_position`: Posição obtida na competição

**Relacionamentos**: Complementa dados da `world_athletics` na `unified_athletes`.

---

**Observações sobre Tabelas Auxiliares:**

- **`instagram`**: Tabela criada para armazenar dados de seguidores do Instagram dos atletas, pensando em futuramente usar isso como um indicador para avaliar o potencial de um atleta e suas chances de chegar longe na carreira.

- **`athlete_history`**: Tabela desenvolvida para guardar um histórico das últimas atualizações realizadas nos dados dos atletas, permitindo auditoria e controle de mudanças.


## 3. Desenvolvimento (Arquivos e Scripts)

### 3.1. .env

**Descrição**: Arquivo de configuração com variáveis de ambiente essenciais para o funcionamento do projeto.

**Acesso ao arquivo**: [.env](/.env)

**Variáveis Principais**:
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=senha
DB_NAME=sportheca
DB_PORT=3306
```

**Importância**:
- Centraliza configurações sensíveis
- Permite diferentes ambientes (desenvolvimento, produção)
- Mantém credenciais seguras fora do código

### 3.2. scripts/

Conjunto de scripts especializados em coleta de dados de diferentes fontes:

#### 4.2.1. scraper-world-athletics.js
- **Função**: Coleta dados do ranking mundial da World Athletics
- **Origem**: Site oficial da World Athletics
- **Destino**: Tabela `world_athletics`
- **Dados coletados**: Rankings, tempos, países, eventos

#### 3.2.2. scraper-olympics.js
- **Função**: Coleta resultados olímpicos oficiais
- **Origem**: Site oficial das Olimpíadas
- **Destino**: Tabelas `olympics_individual` e `olympics_equipe`
- **Dados coletados**: Resultados, posições, medalhas

#### 3.2.3. scraper-athlete-details.js
- **Função**: Coleta informações detalhadas dos atletas
- **Origem**: Site oficial da World Athletics
- **Destino**: Tabela `athlete_details`
- **Dados coletados**: Dados pessoais e resultados de cada atleta.

#### 3.2.4. script3-unified-migration.js
- **Função**: Script principal que orquestra todo o processo de migração e unificação dos dados coletados pelos scrapers, transformando dados brutos em informações estruturadas e calculando métricas de performance.

**Etapas**:

**Etapa 1 - Migração de Dados Básicos**:
- Busca todos os atletas da tabela `world_athletics`
- Para cada atleta, coleta seus resultados detalhados da tabela `test_results`
- Converte tempos para centésimos de segundo para facilitar comparações
- Calcula idade automaticamente a partir da data de nascimento
- Insere/atualiza dados na tabela `unified_athletes` com informações completas
- Cria um registro por atleta por evento (ex: um atleta que corre 100m e 200m terá 2 registros)

**Etapa 2 - Identificação Olímpica**:
- Percorre cada registro da `unified_athletes` (atleta + evento específico)
- Normaliza nomes de eventos para corresponder aos eventos olímpicos
- Verifica se o atleta participou das Olimpíadas naquele evento específico
- Identifica se conquistou medalha (posições 1, 2 ou 3)
- Atualiza campos `is_olympic_athlete` e `has_olympic_medal`

**Etapa 3 - Cálculo de Scores**:
- Busca atletas de eventos cronometrados com gênero definido
- Identifica o melhor tempo olímpico (ouro) para cada evento/gênero
- Aplica fórmula matemática para calcular score comparativo
- Atualiza campo `calculation_score` na tabela unificada

**Fórmula de Cálculo do Score de Provas Cronometradas**:
<div align="center">
  <sub>Figura 1 - Fórmula usada no cálculo de score</sub> <br>

  <img src="../bi-vib3-data-crawler/assets/calculo-score.png" alt="cálculo-score" style="max-width: 800px; width: 100%; height: auto;">

  <sup>Fonte: Material produzido pelos autores (2025).</sup>
</div>


### 3.3. core.js

**Descrição**: Script principal que coordena todos os outros scripts e processos. É o ponto de entrada único para executar qualquer funcionalidade do projeto.

**O que ele faz de forma simples**:
- Funciona como um menu de comandos para rodar diferentes partes do sistema
- Conecta com o banco de dados automaticamente
- Chama os scripts corretos baseado no comando que você digita
- Trata erros e mostra mensagens claras sobre o que está acontecendo
- Garante que tudo seja executado na ordem correta

**Comandos Disponíveis e suas Funções**:

**Coleta de Dados (Web Scraping)**:
```bash
# Coleta ranking mundial masculino e feminino da World Athletics
node core.js scraper-world-athletics

# Coleta resultados olímpicos de todos os eventos
node core.js scraper-olympics

# Coleta detalhes e histórico de resultados dos atletas
node core.js scraper-athlete-details
```

**Processamento e Unificação**:
```bash
# Etapa 1: Une dados básicos (world_athletics + test_results)
node core.js unified-migration etapa1

# Etapa 2: Identifica quem é atleta olímpico e medalhista
node core.js unified-migration etapa2

# Etapa 3: Calcula scores de performance de todos os atletas
node core.js unified-migration etapa3
```

**Fluxo Recomendado de Execução**:
1. Primeiro rode os scrapers para coletar dados
2. Depois execute as etapas de migração em ordem (1 → 2 → 3)
3. Cada comando mostra progresso e estatísticas em tempo real

### 3.4. models/

**Descrição**: Responsável pela estruturação e manipulação das tabelas do banco de dados usando Sequelize ORM.

**Arquivos Principais**:
- `index.js`: Configuração principal do Sequelize e conexão com banco
- `UnifiedAthletes.js`: Modelo da tabela principal ([ver seção 2.1](#21-unified_athletes))
- `OlympicsIndividual.js`: Modelo para dados olímpicos individuais ([ver seção 2.2](#22-olympics_individual))
- `OlympicsEquipe.js`: Modelo para dados olímpicos de equipe ([ver seção 2.3](#23-olympics_equipe))
- `WorldAthletics.js`: Modelo para dados da World Athletics ([ver seção 2.4](#24-world_athletics))
- `TestResults.js`: Modelo para resultados complementares ([ver seção 2.5](#25-test_results))
- `Instagram.js`: Modelo auxiliar para dados do Instagram (funcionalidade futura)
- `AthleteHistory.js`: Modelo auxiliar para histórico de atualizações

**Funcionalidades**:
- Define estrutura das tabelas e relacionamentos
- Fornece interface para operações CRUD
- Implementa validações de dados
- Gerencia migrações e sincronização

### 3.5. utils/

**Descrição**: Conjunto de funções auxiliares reutilizáveis que dão suporte ao projeto.

**Funcionalidades Implementadas**:
- **Validação de dados**: Funções para verificar integridade dos dados coletados
- **Formatação**: Normalização de nomes, países e formatos de tempo
- **Conversão de tempo**: Transformação de diferentes formatos para centésimos de segundo
- **Cálculo de idade**: Computação automática baseada em data de nascimento
- **Mapeamento de eventos**: Correspondência entre nomes genéricos e específicos de eventos
- **Limpeza de dados**: Remoção de caracteres especiais e padronização

**Importância**:
- Mantém o código modular e reutilizável
- Centraliza lógicas complexas de processamento
- Facilita manutenção e testes
- Promove consistência entre diferentes scripts

---

## 4. Próximos Passos

### 4.1. Infraestrutura e Desempenho

**Deploy em nuvem**: O sistema atualmente roda localmente, o que consome muita memória e processamento. A migração para a nuvem (como AWS ou outro serviço escalável) vai garantir mais estabilidade e escalabilidade.

**Atualizações automáticas**: Hoje, a atualização dos dados exige a execução manual de todos os scripts. O próximo passo é implementar um agendamento periódico (como um cron job) para automatizar esse processo diariamente.

### 4.2. Visualização e Acesso

**Tela de visualização dos dados**: Criar uma interface web simples para exibir os principais dados e métricas coletadas. Isso facilitará a análise e o acompanhamento dos atletas, reduzindo a dependência de consultas SQL.

### 4.3. Expansão de Métricas

**Cálculo de score para eventos não cronometrados**: O score atual foi implementado para provas com medição de tempo. O próximo passo é adaptar a lógica para eventos com outras métricas (como distância, por exemplo).

### 4.4. Automatização e Monitoramento



**Monitoramento de saúde**: Criar alertas para identificar falhas ou comportamentos anômalos nos processos de coleta e carga.

**Backup automatizado**: Estabelecer uma rotina de backup incremental. Já existe a tabela `athlete_history`, responsável por armazenar versões históricas dos dados dos atletas — o que permite acompanhar a evolução ao longo do tempo e recuperar informações em caso de falhas.



---

## Métricas do Projeto

- **Atletas processados**: ~50.000+
- **Eventos cobertos**: 40+ modalidades
- **Fontes de dados**: 2 (World Athletics, Olympics)
- **Precisão de dados**: 95%+ (baseado em validações cruzadas)
- **Tempo de processamento**: ~30 minutos para dataset completo

---

## Tecnologias Utilizadas

- **Node.js**: Runtime JavaScript
- **Sequelize**: ORM para banco de dados
- **MySQL**: Sistema de gerenciamento de banco de dados


---

*Documentação criada em: Julho 2025*

*Última atualização: Julho 2025*

