const mysql = require('mysql2/promise');
const logger = require('./logger');

class Database {
  constructor() {
    this.connection = null;
    this.config = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '@Sportheca123',
      database: process.env.DB_NAME || 'sportheca_athletes',
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    };
  }

  // Conecta ao banco de dados
  async connect() {
    try {
      this.connection = await mysql.createConnection(this.config);
      logger.info('Conectado ao banco de dados MySQL');
      return this.connection;
    } catch (error) {
      logger.error('Erro ao conectar ao banco de dados:', error);
      throw error;
    }
  }

  // Executa uma query
  async query(sql, params = []) {
    try {
      if (!this.connection) {
        await this.connect();
      }
      
      const [rows, fields] = await this.connection.execute(sql, params);
      return { rows, fields };
    } catch (error) {
      logger.error('Erro ao executar query:', { sql, params, error: error.message });
      throw error;
    }
  }

  // Insere dados na tabela World Athletics
  async insertWorldAthletics(athleteData) {
    const sql = `
      INSERT INTO world_athletics (
        athlete_name, world_ranking, birth_date, score, event, category, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
      ON DUPLICATE KEY UPDATE
        world_ranking = VALUES(world_ranking),
        birth_date = VALUES(birth_date),
        score = VALUES(score),
        event = VALUES(event),
        category = VALUES(category),
        updated_at = NOW()
    `;

    const params = [
      athleteData.athlete_name,
      athleteData.world_ranking,
      athleteData.birth_date,
      athleteData.score,
      athleteData.event,
      athleteData.category
    ];

    return await this.query(sql, params);
  }

  // Insere dados na tabela Olympics
  async insertOlympics(athleteData) {
    const sql = `
      INSERT INTO olympics (
        athlete_name, is_olympic, olympic_medals, created_at, updated_at
      ) VALUES (?, ?, ?, NOW(), NOW())
      ON DUPLICATE KEY UPDATE
        is_olympic = VALUES(is_olympic),
        olympic_medals = VALUES(olympic_medals),
        updated_at = NOW()
    `;

    const params = [
      athleteData.athlete_name,
      athleteData.is_olympic,
      athleteData.olympic_medals
    ];

    return await this.query(sql, params);
  }

  // Insere dados na tabela Unificada
  async insertUnifiedAthlete(athleteData) {
    const sql = `
      INSERT INTO unified_athletes (
        athlete_name, world_ranking, birth_date, score, event, category, 
        is_olympic, olympic_medals, instagram_followers, source, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      ON DUPLICATE KEY UPDATE
        world_ranking = VALUES(world_ranking),
        birth_date = VALUES(birth_date),
        score = VALUES(score),
        event = VALUES(event),
        category = VALUES(category),
        is_olympic = VALUES(is_olympic),
        olympic_medals = VALUES(olympic_medals),
        instagram_followers = VALUES(instagram_followers),
        source = VALUES(source),
        updated_at = NOW()
    `;

    const params = [
      athleteData.athlete_name,
      athleteData.world_ranking,
      athleteData.birth_date,
      athleteData.score,
      athleteData.event,
      athleteData.category,
      athleteData.is_olympic,
      athleteData.olympic_medals,
      athleteData.instagram_followers,
      athleteData.source
    ];

    return await this.query(sql, params);
  }

  // Insere dados na tabela Instagram
  async insertInstagram(athleteData) {
    const sql = `
      INSERT INTO instagram (
        athlete_name, instagram_followers, created_at, updated_at
      ) VALUES (?, ?, NOW(), NOW())
      ON DUPLICATE KEY UPDATE
        instagram_followers = VALUES(instagram_followers),
        updated_at = NOW()
    `;

    const params = [
      athleteData.athlete_name,
      athleteData.instagram_followers
    ];

    return await this.query(sql, params);
  }

  // Insere histórico de atualizações
  async insertHistory(athleteName, changes) {
    const sql = `
      INSERT INTO athlete_history (
        athlete_name, changes, created_at
      ) VALUES (?, ?, NOW())
    `;

    const params = [athleteName, JSON.stringify(changes)];
    return await this.query(sql, params);
  }

  // Busca atleta por nome na tabela Unificada
  async findAthleteByName(name) {
    const sql = 'SELECT * FROM unified_athletes WHERE athlete_name = ?';
    const result = await this.query(sql, [name]);
    return result.rows[0] || null;
  }

  // Lista todos os atletas da tabela Unificada
  async getAllAthletes() {
    const sql = 'SELECT * FROM unified_athletes ORDER BY athlete_name';
    const result = await this.query(sql);
    return result.rows;
  }

  // Fecha conexão
  async close() {
    if (this.connection) {
      await this.connection.end();
      logger.info('Conexão com banco de dados fechada');
    }
  }

  // Testa conexão
  async testConnection() {
    try {
      await this.connect();
      const result = await this.query('SELECT 1 as test');
      logger.info('Teste de conexão com banco bem-sucedido');
      return true;
    } catch (error) {
      logger.error('Falha no teste de conexão:', error);
      return false;
    }
  }
}

module.exports = new Database();