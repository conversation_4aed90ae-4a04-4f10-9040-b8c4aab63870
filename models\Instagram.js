module.exports = (sequelize, DataTypes) => {
  const Instagram = sequelize.define('Instagram', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    athlete_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true
    },
    instagram_followers: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'instagram',
    timestamps: false
  });
  return Instagram;
}; 