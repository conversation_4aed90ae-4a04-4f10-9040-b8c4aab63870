// Importa função para coletar dados do ranking mundial de atletismo
const { scrapeWorldAthletics } = require('./Scripts/scraper-world-athletics');

// Importa função para coletar dados dos resultados olímpicos
const { scrapeOlympics } = require('./Scripts/scraper-olympics');

// Importa funções para coletar detalhes e resultados dos atletas
const {
  scrapeAthleteDetails,
  scrapeAthleteDetailsLimited,
  resumeAthleteDetails,
  resumeAthleteDetailsWithLimit,
  clearCheckpoint
} = require('./Scripts/scraper-athlete-details');

// Importa funções para migração e processamento unificado dos dados
const {
  runEtapa1,
  runEtapa2,
  runEtapa3
} = require('./Scripts/script3-unified-migration');

// Importa modelos do banco de dados
const db = require('./models');

// Função principal que coordena a execução dos diferentes scrapers baseado nos argumentos da linha de comando
async function main() {
  const scraper = process.argv[2];
  const param = process.argv[3];
  const option = process.argv[4];
  const extraParam = process.argv[5];

  // Exibe instruções de uso se nenhum scraper for especificado
  if (!scraper) {
    console.log('Uso: node core.js <scraper> [parametros] [opcoes]');
    console.log('Exemplos:');
    console.log('  node core.js world-athletics men');
    console.log('  node core.js olympics');
    console.log('  node core.js athlete-details men');
    console.log('  node core.js athlete-details men limited    # Apenas 10 páginas');
    console.log('  node core.js athlete-details men 5          # Apenas 5 páginas');
    console.log('  node core.js athlete-details men resume     # Retoma do checkpoint');
    console.log('  node core.js athlete-details men resume 10  # Retoma + 10 páginas');
    console.log('  node core.js athlete-details men clear      # Limpa progresso e recomeça');
    process.exit(1);
  }

  // Estabelece conexão com o banco de dados
  await db.sequelize.authenticate();
  console.log('Conectado ao banco via Sequelize!');

  // Executa scraper do World Athletics para coletar ranking mundial
  if (scraper === 'world-athletics') {
    const gender = param || 'men';
    console.log(`Rodando scraper World Athletics para gênero: ${gender}`);
    const atletas = await scrapeWorldAthletics(gender);
    console.log(`Total de atletas coletados: ${atletas.length}`);

    // Salva cada atleta coletado no banco de dados
    for (const atleta of atletas) {
      console.log("Salvando atleta: ", atleta.athlete_name);
      await db.WorldAthletics.upsert(atleta);
    }
    console.log('Dados salvos na tabela world_athletics!');

  // Executa scraper dos dados olímpicos
  } else if (scraper === 'olympics') {
    console.log('Rodando scraper Olympics...');
    const result = await scrapeOlympics();

    console.log(`Atletas individuais coletados: ${result.individual.length}`);
    console.log(`Equipes coletadas: ${result.equipe.length}`);

    // Salva dados dos atletas que competiram individualmente
    for (const atleta of result.individual) {
      console.log("Salvando atleta individual: ", atleta.athlete_name);
      await db.OlympicsIndividual.upsert({
        athlete_name: atleta.athlete_name,
        event: atleta.event,
        country: atleta.country,
        result: atleta.result,
        position: atleta.position
      });
    }

    // Salva dados das equipes que competiram
    for (const equipe of result.equipe) {
      console.log("Salvando equipe: ", equipe.event);
      await db.OlympicsEquipe.upsert({
        event: equipe.event,
        equipe: equipe.equipe,
        result: equipe.result,
        position: equipe.position
      });
    }

    console.log('Dados salvos nas tabelas olympics_individual e olympics_equipe!');
  // Executa scraper para coletar detalhes e resultados dos atletas
  } else if (scraper === 'athlete-details') {
    const gender = param || 'men';

    console.log(`Rodando scraper Athlete Details para gênero: ${gender}`);

    // Limpa checkpoint anterior se solicitado
    if (option === 'clear') {
      console.log('Limpando checkpoint anterior...');
      clearCheckpoint(gender);
      console.log('Checkpoint limpo! Execute novamente sem a opção "clear".');
      return;
    }

    let result;

    // Retoma processamento a partir do último checkpoint salvo
    if (option === 'resume') {
      if (extraParam && !isNaN(parseInt(extraParam))) {
        const additionalPages = parseInt(extraParam);
        console.log(`Retomando do checkpoint + ${additionalPages} páginas...`);
        result = await resumeAthleteDetailsWithLimit(gender, additionalPages, db.TestResults);
      } else {
        console.log('Retomando do checkpoint...');
        result = await resumeAthleteDetails(gender, db.TestResults);
      }
    // Processa apenas um número limitado de páginas
    } else if (option === 'limited') {
      console.log('Modo limitado: processando apenas 10 páginas...');
      result = await scrapeAthleteDetailsLimited(gender, 10, db.TestResults);
    } else if (!isNaN(parseInt(option))) {
      const maxPages = parseInt(option);
      console.log(`Processando apenas ${maxPages} páginas...`);
      result = await scrapeAthleteDetailsLimited(gender, maxPages, db.TestResults);
    // Processa todas as páginas disponíveis
    } else {
      console.log('Processando todas as páginas...');
      result = await scrapeAthleteDetails(gender, { dbModel: db.TestResults });
    }

    // Exibe resultado do processamento
    if (result.success) {
      console.log(`Processamento concluído! ${result.totalProcessed} atletas processados.`);
      console.log('Dados salvos na tabela test_results!');
    } else {
      console.error(`Erro durante o processamento: ${result.error}`);
      console.log('Progresso salvo em checkpoint - use "resume" para continuar.');
    }
  // Executa processo de migração unificada dos dados
  } else if (scraper === 'unified-migration') {
    const etapa = param || 'help';

    console.log(`Rodando scraper Unified Migration - ${etapa}`);

    if (etapa === 'etapa1') {
      await runEtapa1();
    } else if (etapa === 'etapa2') {
      await runEtapa2();
    } else if (etapa === 'etapa3') {
      await runEtapa3();
    } else {
      console.log('Etapas disponíveis: etapa1, etapa2, etapa3');
      console.log('Exemplos:');
      console.log('  node core.js unified-migration etapa1  # Migração completa');
      console.log('  node core.js unified-migration etapa2  # Status olímpico');
      console.log('  node core.js unified-migration etapa3  # Cálculo de scores');
    }
  } else {
    console.log('Scraper não reconhecido:', scraper);
  }

  // Fecha conexão com o banco de dados
  await db.sequelize.close();
}

// Executa função principal e trata erros
main().catch(err => {
  console.error('Erro no core:', err);
  process.exit(1);
});