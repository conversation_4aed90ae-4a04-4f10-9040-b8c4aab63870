module.exports = (sequelize, DataTypes) => {
  const OlympicsEquipe = sequelize.define('OlympicsEquipe', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    event: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    equipe: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    result: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    position: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    tableName: 'olympics_equipe',
    timestamps: false
  });
  return OlympicsEquipe;
}; 