module.exports = (sequelize, DataTypes) => {
  const TestResults = sequelize.define('TestResults', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    athlete_id: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    athlete_name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    competition_date: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    discipline: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    result_mark: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    competition_name: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    place_position: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'test_results',
    timestamps: false
  });
  return TestResults;
}; 